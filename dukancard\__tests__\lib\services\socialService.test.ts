import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { subscriptionsService, likesService, reviewsService, getActivityMetrics } from '@/lib/services/socialService';

// Create a comprehensive mock for the entire Supabase client
const mockSupabaseClient = {
  from: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  ilike: jest.fn().mockReturnThis(),
  order: jest.fn().mockReturnThis(),
  range: jest.fn().mockReturnThis(),
  delete: jest.fn().mockReturnThis(),
  update: jest.fn().mockReturnThis(),
  in: jest.fn().mockReturnThis(),
} as any;

// Mock the Supabase client
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn(() => mockSupabaseClient),
}));

// Mock the Supabase SSR module
jest.mock('@supabase/ssr', () => ({
  createServerClient: jest.fn(() => mockSupabaseClient),
}));

// Mock the Next.js cookies function
jest.mock('next/headers', () => ({
  cookies: jest.fn(() => ({
    get: jest.fn(),
    set: jest.fn(),
    delete: jest.fn(),
  })),
}));

// Mock constants
jest.mock('@/lib/supabase/constants', () => ({
  TABLES: {
    SUBSCRIPTIONS: 'subscriptions',
    BUSINESS_PROFILES: 'business_profiles',
    LIKES: 'likes',
    RATINGS_REVIEWS: 'ratings_reviews',
    CUSTOMER_PROFILES: 'customer_profiles',
  },
  COLUMNS: {
    ID: 'id',
    USER_ID: 'user_id',
    BUSINESS_PROFILE_ID: 'business_profile_id',
    BUSINESS_NAME: 'business_name',
    CREATED_AT: 'created_at',
    RATING: 'rating',
    REVIEW_TEXT: 'review_text',
    NAME: 'name',
    EMAIL: 'email',
    AVATAR_URL: 'avatar_url',
    BUSINESS_SLUG: 'business_slug',
    LOGO_URL: 'logo_url',
    CITY: 'city',
    STATE: 'state',
    PINCODE: 'pincode',
    ADDRESS_LINE: 'address_line',
    UPDATED_AT: 'updated_at',
  },
}));

describe('socialService', () => {
  beforeEach(() => {
    // Set environment variables for tests
    process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key';

    // Reset all mocks
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('subscriptionsService', () => {
    describe('fetchSubscriptions', () => {
      it('should fetch subscriptions with pagination', async () => {
        const mockData = [
          {
            id: '1',
            business_profile_id: 'business1',
            business_profiles: {
              id: 'business1',
              business_name: 'Test Business',
              business_slug: 'test-business',
            },
          },
        ];

        mockSupabaseClient.select.mockResolvedValueOnce({
          data: mockData,
          count: 1,
          error: null,
        });

        const result = await subscriptionsService.fetchSubscriptions(
          'user1',
          1,
          10,
          ''
        );

        expect(result.items).toEqual(mockData);
        expect(result.totalCount).toBe(1);
        expect(result.hasMore).toBe(false);
        expect(result.currentPage).toBe(1);
      });

      it('should handle search term', async () => {
        mockSupabaseClient.select.mockResolvedValueOnce({
          data: [],
          count: 0,
          error: null,
        });

        await subscriptionsService.fetchSubscriptions(
          'user1',
          1,
          10,
          'search term'
        );

        expect(mockSupabaseClient.ilike).toHaveBeenCalledWith(
          'business_profiles.business_name',
          '%search term%'
        );
      });

      it('should handle errors', async () => {
        mockSupabaseClient.select.mockResolvedValueOnce({
          data: null,
          count: null,
          error: { message: 'Database error' },
        });

        await expect(
          subscriptionsService.fetchSubscriptions('user1', 1, 10, '')
        ).rejects.toThrow('Database error');
      });

      it('should return empty result when no subscriptions found', async () => {
        mockSupabaseClient.select.mockResolvedValueOnce({
          data: null,
          count: 0,
          error: null,
        });

        const result = await subscriptionsService.fetchSubscriptions('user1', 1, 10, '');

        expect(result.items).toEqual([]);
        expect(result.totalCount).toBe(0);
        expect(result.hasMore).toBe(false);
        expect(result.currentPage).toBe(1);
      });

      it('should calculate hasMore correctly when there are more pages', async () => {
        const mockData = Array(10).fill({
          id: '1',
          business_profiles: { id: 'business1', business_name: 'Test Business' },
        });

        mockSupabaseClient.select.mockResolvedValueOnce({
          data: mockData,
          count: 25,
          error: null,
        });

        const result = await subscriptionsService.fetchSubscriptions('user1', 1, 10, '');

        expect(result.hasMore).toBe(true);
        expect(result.totalCount).toBe(25);
      });

      it('should handle array business_profiles correctly', async () => {
        const mockData = [
          {
            id: '1',
            business_profiles: [{ id: 'business1', business_name: 'Test Business' }],
          },
        ];

        mockSupabaseClient.select.mockResolvedValueOnce({
          data: mockData,
          count: 1,
          error: null,
        });

        const result = await subscriptionsService.fetchSubscriptions('user1', 1, 10, '');

        expect(result.items[0].business_profiles).toEqual({
          id: 'business1',
          business_name: 'Test Business',
        });
      });
    });

    describe('unsubscribe', () => {
      it('should delete subscription successfully', async () => {
        mockSupabaseClient.delete.mockResolvedValueOnce({
          error: null,
        });

        await subscriptionsService.unsubscribe('subscription1');

        expect(mockSupabaseClient.from).toHaveBeenCalledWith('subscriptions');
        expect(mockSupabaseClient.delete).toHaveBeenCalled();
        expect(mockSupabaseClient.eq).toHaveBeenCalledWith('id', 'subscription1');
      });

      it('should handle delete errors', async () => {
        mockSupabaseClient.delete.mockResolvedValueOnce({
          error: { message: 'Delete failed' },
        });

        await expect(
          subscriptionsService.unsubscribe('subscription1')
        ).rejects.toThrow('Delete failed');
      });
    });

    describe('fetchBusinessFollowers', () => {
      it('should fetch business followers with pagination', async () => {
        const mockSubscriptions = [
          { id: 'sub1', user_id: 'user1', created_at: '2024-01-01' },
          { id: 'sub2', user_id: 'user2', created_at: '2024-01-02' },
        ];

        const mockCustomerProfiles = [
          { id: 'user1', name: 'Customer One', email: '<EMAIL>', avatar_url: null },
        ];

        const mockBusinessProfiles = [
          { id: 'user2', business_name: 'Business Two', business_slug: 'business-two', logo_url: null },
        ];

        // Mock count query
        mockSupabaseClient.select.mockResolvedValueOnce({
          count: 2,
          error: null,
        });

        // Mock subscriptions query
        mockSupabaseClient.select.mockResolvedValueOnce({
          data: mockSubscriptions,
          error: null,
        });

        // Mock customer profiles query
        mockSupabaseClient.select.mockResolvedValueOnce({
          data: mockCustomerProfiles,
          error: null,
        });

        // Mock business profiles query
        mockSupabaseClient.select.mockResolvedValueOnce({
          data: mockBusinessProfiles,
          error: null,
        });

        const result = await subscriptionsService.fetchBusinessFollowers('business1', 1, 10);

        expect(result.items).toHaveLength(2);
        expect(result.totalCount).toBe(2);
        expect(result.hasMore).toBe(false);
        expect(result.currentPage).toBe(1);
        expect(mockSupabaseClient.in).toHaveBeenCalledWith('id', ['user1', 'user2']);
      });

      it('should return empty result when no followers found', async () => {
        mockSupabaseClient.select.mockResolvedValueOnce({
          count: 0,
          error: null,
        });

        const result = await subscriptionsService.fetchBusinessFollowers('business1', 1, 10);

        expect(result.items).toEqual([]);
        expect(result.totalCount).toBe(0);
        expect(result.hasMore).toBe(false);
        expect(result.currentPage).toBe(1);
      });

      it('should handle errors during count query', async () => {
        mockSupabaseClient.select.mockResolvedValueOnce({
          count: null,
          error: { message: 'Count error' },
        });

        await expect(
          subscriptionsService.fetchBusinessFollowers('business1', 1, 10)
        ).rejects.toThrow('Failed to fetch subscription count');
      });

      it('should handle errors during subscriptions query', async () => {
        mockSupabaseClient.select
          .mockResolvedValueOnce({ count: 1, error: null })
          .mockResolvedValueOnce({ data: null, error: { message: 'Subscriptions error' } });

        await expect(
          subscriptionsService.fetchBusinessFollowers('business1', 1, 10)
        ).rejects.toThrow('Failed to fetch subscriptions');
      });

      it('should handle errors during customer profiles query', async () => {
        mockSupabaseClient.select
          .mockResolvedValueOnce({ count: 1, error: null })
          .mockResolvedValueOnce({ data: [{ user_id: 'user1' }], error: null })
          .mockResolvedValueOnce({ data: null, error: { message: 'Customer profiles error' } });

        await expect(
          subscriptionsService.fetchBusinessFollowers('business1', 1, 10)
        ).rejects.toThrow('Failed to fetch customer profiles');
      });

      it('should handle errors during business profiles query', async () => {
        mockSupabaseClient.select
          .mockResolvedValueOnce({ count: 1, error: null })
          .mockResolvedValueOnce({ data: [{ user_id: 'user1' }], error: null })
          .mockResolvedValueOnce({ data: [], error: null })
          .mockResolvedValueOnce({ data: null, error: { message: 'Business profiles error' } });

        await expect(
          subscriptionsService.fetchBusinessFollowers('business1', 1, 10)
        ).rejects.toThrow('Failed to fetch business profiles');
      });
    });
  });

  describe('likesService', () => {
    describe('fetchLikes', () => {
      it('should fetch likes with pagination', async () => {
        const mockCountData = { count: 5, error: null };
        const mockData = [
          {
            id: '1',
            business_profiles: {
              id: 'business1',
              business_name: 'Test Business',
            },
          },
        ];

        // Mock count query
        mockSupabaseClient.select.mockResolvedValueOnce(mockCountData);
        // Mock data query
        mockSupabaseClient.select.mockResolvedValueOnce({
          data: mockData,
          error: null,
        });

        const result = await likesService.fetchLikes(
          'user1',
          1,
          10,
          'newest'
        );

        expect(result.items).toEqual(mockData);
        expect(result.totalCount).toBe(5);
        expect(result.hasMore).toBe(false);
        expect(result.currentPage).toBe(1);
      });

      it('should handle search term correctly', async () => {
        mockSupabaseClient.select
          .mockResolvedValueOnce({ count: 0, error: null })
          .mockResolvedValueOnce({ data: [], error: null });

        await likesService.fetchLikes('user1', 1, 10, 'search term');

        expect(mockSupabaseClient.ilike).toHaveBeenCalledWith(
          'business_profiles.business_name',
          '%search term%'
        );
      });

      it('should return empty result when no likes found', async () => {
        mockSupabaseClient.select
          .mockResolvedValueOnce({ count: 0, error: null })
          .mockResolvedValueOnce({ data: [], error: null });

        const result = await likesService.fetchLikes('user1', 1, 10, '');

        expect(result.items).toEqual([]);
        expect(result.totalCount).toBe(0);
        expect(result.hasMore).toBe(false);
        expect(result.currentPage).toBe(1);
      });

      it('should handle errors during count query', async () => {
        mockSupabaseClient.select.mockResolvedValueOnce({
          count: null,
          error: { message: 'Count error' },
        });

        await expect(
          likesService.fetchLikes('user1', 1, 10, '')
        ).rejects.toThrow('Failed to get likes count');
      });

      it('should handle errors during main data query', async () => {
        mockSupabaseClient.select
          .mockResolvedValueOnce({ count: 1, error: null })
          .mockResolvedValueOnce({ data: null, error: { message: 'Data error' } });

        await expect(
          likesService.fetchLikes('user1', 1, 10, '')
        ).rejects.toThrow('Failed to fetch likes');
      });

      it('should transform array business_profiles correctly', async () => {
        const mockData = [
          {
            id: '1',
            business_profiles: [{ id: 'business1', business_name: 'Test Business' }],
          },
        ];

        mockSupabaseClient.select
          .mockResolvedValueOnce({ count: 1, error: null })
          .mockResolvedValueOnce({ data: mockData, error: null });

        const result = await likesService.fetchLikes('user1', 1, 10, '');

        expect(result.items[0].business_profiles).toEqual({
          id: 'business1',
          business_name: 'Test Business',
        });
      });
    });

    describe('unlike', () => {
      it('should delete like successfully', async () => {
        mockSupabaseClient.delete.mockResolvedValueOnce({
          error: null,
        });

        await likesService.unlike('like1');

        expect(mockSupabaseClient.from).toHaveBeenCalledWith('likes');
        expect(mockSupabaseClient.delete).toHaveBeenCalled();
        expect(mockSupabaseClient.eq).toHaveBeenCalledWith('id', 'like1');
      });

      it('should handle unlike errors', async () => {
        mockSupabaseClient.delete.mockResolvedValueOnce({
          error: { message: 'Delete failed' },
        });

        await expect(likesService.unlike('like1')).rejects.toThrow('Failed to unlike');
      });
    });

    describe('fetchBusinessLikesReceived', () => {
      it('should fetch business likes received with pagination', async () => {
        const mockLikes = [
          { id: 'like1', user_id: 'user1', created_at: '2024-01-01' },
          { id: 'like2', user_id: 'user2', created_at: '2024-01-02' },
        ];

        const mockCustomerProfiles = [
          { id: 'user1', name: 'Customer One', email: '<EMAIL>', avatar_url: null },
        ];

        const mockBusinessProfiles = [
          { id: 'user2', business_name: 'Business Two', business_slug: 'business-two', logo_url: null },
        ];

        // Mock count query
        mockSupabaseClient.select.mockResolvedValueOnce({
          count: 2,
          error: null,
        });

        // Mock likes query
        mockSupabaseClient.select.mockResolvedValueOnce({
          data: mockLikes,
          error: null,
        });

        // Mock customer profiles query
        mockSupabaseClient.select.mockResolvedValueOnce({
          data: mockCustomerProfiles,
          error: null,
        });

        // Mock business profiles query
        mockSupabaseClient.select.mockResolvedValueOnce({
          data: mockBusinessProfiles,
          error: null,
        });

        const result = await likesService.fetchBusinessLikesReceived('business1', 1, 10);

        expect(result.items).toHaveLength(2);
        expect(result.totalCount).toBe(2);
        expect(result.hasMore).toBe(false);
        expect(result.currentPage).toBe(1);
        expect(mockSupabaseClient.in).toHaveBeenCalledWith('id', ['user1', 'user2']);
      });

      it('should return empty result when no likes found', async () => {
        mockSupabaseClient.select.mockResolvedValueOnce({
          count: 0,
          error: null,
        });

        const result = await likesService.fetchBusinessLikesReceived('business1', 1, 10);

        expect(result.items).toEqual([]);
        expect(result.totalCount).toBe(0);
        expect(result.hasMore).toBe(false);
        expect(result.currentPage).toBe(1);
      });

      it('should handle errors during count query', async () => {
        mockSupabaseClient.select.mockResolvedValueOnce({
          count: null,
          error: { message: 'Count error' },
        });

        await expect(
          likesService.fetchBusinessLikesReceived('business1', 1, 10)
        ).rejects.toThrow('Failed to fetch likes count');
      });

      it('should handle errors during likes query', async () => {
        mockSupabaseClient.select
          .mockResolvedValueOnce({ count: 1, error: null })
          .mockResolvedValueOnce({ data: null, error: { message: 'Likes error' } });

        await expect(
          likesService.fetchBusinessLikesReceived('business1', 1, 10)
        ).rejects.toThrow('Failed to fetch likes');
      });

      it('should handle errors during customer profiles query', async () => {
        mockSupabaseClient.select
          .mockResolvedValueOnce({ count: 1, error: null })
          .mockResolvedValueOnce({ data: [{ user_id: 'user1' }], error: null })
          .mockResolvedValueOnce({ data: null, error: { message: 'Customer profiles error' } });

        await expect(
          likesService.fetchBusinessLikesReceived('business1', 1, 10)
        ).rejects.toThrow('Failed to fetch customer profiles');
      });

      it('should handle errors during business profiles query', async () => {
        mockSupabaseClient.select
          .mockResolvedValueOnce({ count: 1, error: null })
          .mockResolvedValueOnce({ data: [{ user_id: 'user1' }], error: null })
          .mockResolvedValueOnce({ data: [], error: null })
          .mockResolvedValueOnce({ data: null, error: { message: 'Business profiles error' } });

        await expect(
          likesService.fetchBusinessLikesReceived('business1', 1, 10)
        ).rejects.toThrow('Failed to fetch business profiles');
      });
    });
  });

  describe('reviewsService', () => {
    describe('fetchReviews', () => {
      it('should fetch reviews with pagination and sorting', async () => {
        const mockData = [
          {
            id: '1',
            rating: 5,
            review_text: 'Great service!',
            created_at: '2024-01-01T00:00:00Z',
            business_profiles: {
              id: 'business1',
              business_name: 'Test Business',
            },
          },
        ];

        mockSupabaseClient.select.mockResolvedValueOnce({
          data: mockData,
          count: 1,
          error: null,
        });

        const result = await reviewsService.fetchReviews(
          'user1',
          1,
          10,
          'rating_high'
        );

        expect(result.items).toEqual(mockData);
        expect(result.totalCount).toBe(1);
        expect(mockSupabaseClient.order).toHaveBeenCalledWith('rating', { ascending: false });
      });

      it('should handle search term correctly', async () => {
        mockSupabaseClient.select
          .mockResolvedValueOnce({ count: 0, error: null })
          .mockResolvedValueOnce({ data: [], error: null });

        await reviewsService.fetchReviews('user1', 1, 10, 'newest', 'search term');

        expect(mockSupabaseClient.ilike).toHaveBeenCalledWith(
          'business_profiles.business_name',
          '%search term%'
        );
      });

      it('should return empty result when no reviews found', async () => {
        mockSupabaseClient.select
          .mockResolvedValueOnce({ count: 0, error: null })
          .mockResolvedValueOnce({ data: [], error: null });

        const result = await reviewsService.fetchReviews('user1', 1, 10, 'newest');

        expect(result.items).toEqual([]);
        expect(result.totalCount).toBe(0);
        expect(result.hasMore).toBe(false);
        expect(result.currentPage).toBe(1);
      });

      it('should handle errors during count query', async () => {
        mockSupabaseClient.select.mockResolvedValueOnce({
          count: null,
          error: { message: 'Count error' },
        });

        await expect(
          reviewsService.fetchReviews('user1', 1, 10, 'newest')
        ).rejects.toThrow('Failed to get reviews count');
      });

      it('should handle errors during main data query', async () => {
        mockSupabaseClient.select
          .mockResolvedValueOnce({ count: 1, error: null })
          .mockResolvedValueOnce({ data: null, error: { message: 'Data error' } });

        await expect(
          reviewsService.fetchReviews('user1', 1, 10, 'newest')
        ).rejects.toThrow('Failed to fetch reviews');
      });

      it('should transform array business_profiles correctly', async () => {
        const mockData = [
          {
            id: '1',
            business_profiles: [{ id: 'business1', business_name: 'Test Business' }],
          },
        ];

        mockSupabaseClient.select
          .mockResolvedValueOnce({ count: 1, error: null })
          .mockResolvedValueOnce({ data: mockData, error: null });

        const result = await reviewsService.fetchReviews('user1', 1, 10, 'newest');

        expect(result.items[0].business_profiles).toEqual({
          id: 'business1',
          business_name: 'Test Business',
        });
      });
    });

    describe('deleteReview', () => {
      it('should delete review successfully', async () => {
        mockSupabaseClient.delete.mockResolvedValueOnce({
          error: null,
        });

        await reviewsService.deleteReview('review1');

        expect(mockSupabaseClient.from).toHaveBeenCalledWith('ratings_reviews');
        expect(mockSupabaseClient.delete).toHaveBeenCalled();
        expect(mockSupabaseClient.eq).toHaveBeenCalledWith('id', 'review1');
      });
    });

    describe('updateReview', () => {
      it('should update review successfully', async () => {
        mockSupabaseClient.update.mockResolvedValueOnce({
          error: null,
        });

        await reviewsService.updateReview(
          'review1',
          4,
          'Updated review text'
        );

        expect(mockSupabaseClient.from).toHaveBeenCalledWith('ratings_reviews');
        expect(mockSupabaseClient.update).toHaveBeenCalledWith({
          rating: 4,
          review_text: 'Updated review text',
          updated_at: expect.any(String),
        });
        expect(mockSupabaseClient.eq).toHaveBeenCalledWith('id', 'review1');
      });

      it('should handle update errors', async () => {
        mockSupabaseClient.update.mockResolvedValueOnce({
          error: { message: 'Update failed' },
        });

        await expect(
          reviewsService.updateReview('review1', 4, 'Updated review text')
        ).rejects.toThrow('Failed to update review');
      });
    });

    describe('fetchBusinessReviewsReceived', () => {
      it('should fetch business reviews received with pagination', async () => {
        const mockReviews = [
          { id: 'review1', user_id: 'user1', rating: 5, review_text: 'Great!', created_at: '2024-01-01' },
          { id: 'review2', user_id: 'user2', rating: 4, review_text: 'Good!', created_at: '2024-01-02' },
        ];

        const mockCustomerProfiles = [
          { id: 'user1', name: 'Customer One', email: '<EMAIL>', avatar_url: null },
        ];

        const mockBusinessProfiles = [
          { id: 'user2', business_name: 'Business Two', business_slug: 'business-two', logo_url: null },
        ];

        // Mock count query
        mockSupabaseClient.select.mockResolvedValueOnce({
          count: 2,
          error: null,
        });

        // Mock reviews query
        mockSupabaseClient.select.mockResolvedValueOnce({
          data: mockReviews,
          error: null,
        });

        // Mock customer profiles query
        mockSupabaseClient.select.mockResolvedValueOnce({
          data: mockCustomerProfiles,
          error: null,
        });

        // Mock business profiles query
        mockSupabaseClient.select.mockResolvedValueOnce({
          data: mockBusinessProfiles,
          error: null,
        });

        const result = await reviewsService.fetchBusinessReviewsReceived('business1', 1, 10, 'newest');

        expect(result.items).toHaveLength(2);
        expect(result.totalCount).toBe(2);
        expect(result.hasMore).toBe(false);
        expect(result.currentPage).toBe(1);
        expect(mockSupabaseClient.in).toHaveBeenCalledWith('id', ['user1', 'user2']);
      });

      it('should return empty result when no reviews found', async () => {
        mockSupabaseClient.select.mockResolvedValueOnce({
          count: 0,
          error: null,
        });

        const result = await reviewsService.fetchBusinessReviewsReceived('business1', 1, 10, 'newest');

        expect(result.items).toEqual([]);
        expect(result.totalCount).toBe(0);
        expect(result.hasMore).toBe(false);
        expect(result.currentPage).toBe(1);
      });

      it('should handle errors during count query', async () => {
        mockSupabaseClient.select.mockResolvedValueOnce({
          count: null,
          error: { message: 'Count error' },
        });

        await expect(
          reviewsService.fetchBusinessReviewsReceived('business1', 1, 10, 'newest')
        ).rejects.toThrow('Failed to fetch reviews count');
      });
    });
  });

  describe('getActivityMetrics', () => {
    it('should fetch activity metrics for user', async () => {
      // Mock all three queries
      mockSupabaseClient.select
        .mockResolvedValueOnce({ count: 5, error: null }) // likes
        .mockResolvedValueOnce({ count: 3, error: null }) // reviews
        .mockResolvedValueOnce({ count: 2, error: null }); // subscriptions

      const result = await getActivityMetrics('user1');

      expect(result).toEqual({
        likesCount: 5,
        reviewCount: 3,
        subscriptionCount: 2,
        lastUpdated: expect.any(String),
      });
    });

    it('should handle errors in activity metrics', async () => {
      mockSupabaseClient.select.mockResolvedValueOnce({
        count: null,
        error: { message: 'Database error' },
      });

      const result = await getActivityMetrics('user1');
      expect(result).toBeNull();
    });

    it('should handle errors during reviews count query', async () => {
      mockSupabaseClient.select
        .mockResolvedValueOnce({ count: 5, error: null }) // likes
        .mockResolvedValueOnce({ count: null, error: { message: 'Reviews count error' } }) // reviews
        .mockResolvedValueOnce({ count: 2, error: null }); // subscriptions

      const result = await getActivityMetrics('user1');
      expect(result).toBeNull();
    });

    it('should handle errors during subscriptions count query', async () => {
      mockSupabaseClient.select
        .mockResolvedValueOnce({ count: 5, error: null }) // likes
        .mockResolvedValueOnce({ count: 3, error: null }) // reviews
        .mockResolvedValueOnce({ count: null, error: { message: 'Subscriptions count error' } }); // subscriptions

      const result = await getActivityMetrics('user1');
      expect(result).toBeNull();
    });

    it('should return zero counts when no data found', async () => {
      mockSupabaseClient.select
        .mockResolvedValueOnce({ count: 0, error: null }) // likes
        .mockResolvedValueOnce({ count: 0, error: null }) // reviews
        .mockResolvedValueOnce({ count: 0, error: null }); // subscriptions

      const result = await getActivityMetrics('user1');

      expect(result).toEqual({
        likesCount: 0,
        reviewCount: 0,
        subscriptionCount: 0,
      });
    });

    it('should handle null counts gracefully', async () => {
      mockSupabaseClient.select
        .mockResolvedValueOnce({ count: null, error: null }) // likes
        .mockResolvedValueOnce({ count: null, error: null }) // reviews
        .mockResolvedValueOnce({ count: null, error: null }); // subscriptions

      const result = await getActivityMetrics('user1');

      expect(result).toEqual({
        likesCount: 0,
        reviewCount: 0,
        subscriptionCount: 0,
      });
    });
  });

  // Test range calculation fixes for single item fetch issue
  describe('Range Calculation Fixes', () => {
    describe('subscriptionsService.fetchSubscriptions - range calculation', () => {
      it('should use correct range calculation for pagination', async () => {
        const userId = 'test-user-id';
        const page = 2;
        const limit = 10;
        const offset = (page - 1) * limit; // 10

        mockSupabaseClient.select.mockReturnValueOnce({
          data: null,
          count: 25,
          error: null,
        });

        mockSupabaseClient.select.mockReturnValueOnce({
          data: Array(10).fill({ id: 'test', business_profiles: {} }),
          error: null,
        });

        await subscriptionsService.fetchSubscriptions(userId, page, limit, '');

        expect(mockSupabaseClient.range).toHaveBeenCalledWith(offset, offset + limit - 1);
        expect(mockSupabaseClient.range).toHaveBeenCalledWith(10, 19);
      });
    });

    describe('likesService.fetchLikes - range calculation', () => {
      it('should use correct range calculation for pagination', async () => {
        const userId = 'test-user-id';
        const page = 3;
        const limit = 5;
        const offset = (page - 1) * limit; // 10

        mockSupabaseClient.select.mockReturnValueOnce({
          data: null,
          count: 20,
          error: null,
        });

        mockSupabaseClient.select.mockReturnValueOnce({
          data: Array(5).fill({ id: 'test', business_profiles: {} }),
          error: null,
        });

        await likesService.fetchLikes(userId, page, limit, '');

        expect(mockSupabaseClient.range).toHaveBeenCalledWith(offset, offset + limit - 1);
        expect(mockSupabaseClient.range).toHaveBeenCalledWith(10, 14);
      });
    });

    describe('reviewsService.fetchReviews - range calculation', () => {
      it('should use correct range calculation for pagination', async () => {
        const userId = 'test-user-id';
        const page = 1;
        const limit = 20;
        const offset = (page - 1) * limit; // 0

        mockSupabaseClient.select.mockReturnValueOnce({
          data: null,
          count: 50,
          error: null,
        });

        mockSupabaseClient.select.mockReturnValueOnce({
          data: Array(20).fill({ id: 'test', business_profiles: {} }),
          error: null,
        });

        await reviewsService.fetchReviews(userId, page, limit, 'newest', '');

        expect(mockSupabaseClient.range).toHaveBeenCalledWith(offset, offset + limit - 1);
        expect(mockSupabaseClient.range).toHaveBeenCalledWith(0, 19);
      });
    });
  });
});
