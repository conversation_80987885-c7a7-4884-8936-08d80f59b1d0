---
Status: Done
Story: |
  As a developer, I need to implement comprehensive unit and integration tests for the `BusinessReviewsPage` server component in the `dukancard` project. This page is responsible for fetching and displaying "Reviews Received" and "My Reviews" data for a business user, including handling authentication, business profile existence, and fetching review counts. The tests should ensure the page behaves correctly under various conditions, including successful data loads, empty states, error conditions, and proper redirection.

Acceptance Criteria:
- **Authentication & Authorization:**
    - Given an unauthenticated user, when accessing `/dashboard/business/reviews`, then the user is redirected to `/login`.
    - Given an authenticated user without a business profile, when accessing `/dashboard/business/reviews`, then the user is redirected to `/dashboard/business`.
    - Given an authenticated business user, when accessing `/dashboard/business/reviews`, then the page loads successfully.
- **Initial Data Fetching & Rendering:**
    - Given a business user with existing reviews, when the page loads, then it fetches the count of "Reviews Received" (reviews where `business_profile_id` matches user's business ID and `user_id` is different) and "My Reviews" (reviews where `user_id` matches authenticated user's ID).
    - And the `BusinessReviewsPageClient` component receives the correct `businessProfileId`, `reviewsReceivedCount`, and `myReviewsCount` props.
    - And the page correctly displays the `BusinessReviewsPageClient`.
- **Error Handling:**
    - Given an error occurs during fetching review counts, then the `BusinessReviewsPageClient` is still rendered with `reviewsReceivedCount` and `myReviewsCount` set to 0.
    - And the page does not crash.

Tasks:
1. Create a new test file: `C:\web-app\dukancard\__tests__\app\(dashboard)\dashboard\business\reviews\page.test.tsx`.
2. Set up a testing environment that can mock Supabase server-side functions (`createClient`, `supabase.auth.getUser`, `supabase.from`).
3. Write unit tests for the `BusinessReviewsPage` component covering all acceptance criteria.
4. Mock the `supabase.auth.getUser` to simulate authenticated and unauthenticated users.
5. Mock `supabase.from('business_profiles').select().eq().single()` to simulate users with and without business profiles.
6. Mock `supabase.from('ratings_reviews').select().eq().neq().count()` to simulate various review count scenarios (e.g., zero reviews, some reviews, error during fetch).
7. Verify redirects for unauthenticated users and users without business profiles.
8. Verify correct props are passed to `BusinessReviewsPageClient`.
9. Verify error handling when fetching review counts.

File: `C:\web-app\dukancard\app\(dashboard)\dashboard\business\reviews\page.tsx`
Platform: dukancard
---