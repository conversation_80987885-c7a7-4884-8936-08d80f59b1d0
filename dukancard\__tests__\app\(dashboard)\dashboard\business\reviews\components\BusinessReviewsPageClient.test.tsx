import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import '@testing-library/jest-dom';
import BusinessReviewsPageClient from '@/app/(dashboard)/dashboard/business/reviews/components/BusinessReviewsPageClient';

// Mock the child components using the same path as the component imports
jest.mock('@/app/(dashboard)/dashboard/business/reviews/components/BusinessReviewListClient', () => {
  return {
    __esModule: true,
    default: function MockBusinessReviewListClient({ businessProfileId }: { businessProfileId: string }) {
      return (
        <div data-testid="business-review-list-client">
          <div data-testid="business-profile-id">{businessProfileId}</div>
        </div>
      );
    },
  };
});

jest.mock('@/app/(dashboard)/dashboard/business/reviews/components/BusinessMyReviewListClient', () => {
  return {
    __esModule: true,
    default: function MockBusinessMyReviewListClient() {
      return <div data-testid="business-my-review-list-client">My Reviews List</div>;
    },
  };
});

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: React.PropsWithChildren<Record<string, unknown>>) => <div {...props}>{children}</div>,
  },
}));

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  Star: () => <div data-testid="star-icon">Star</div>,
  MessageSquare: () => <div data-testid="message-square-icon">MessageSquare</div>,
}));

// Mock the utils
jest.mock('@/lib/utils', () => ({
  cn: (...classes: (string | undefined | null | boolean)[]) => classes.filter(Boolean).join(' '),
  formatIndianNumberShort: (num: number) => num.toString(),
}));

describe('BusinessReviewsPageClient', () => {
  const defaultProps = {
    businessProfileId: 'business-123',
    reviewsReceivedCount: 15,
    myReviewsCount: 8,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Initial Rendering', () => {
    it('should render the header with correct title and description', () => {
      render(<BusinessReviewsPageClient {...defaultProps} />);

      expect(screen.getByText('Business Reviews')).toBeInTheDocument();
      expect(screen.getByText(/Monitor customer feedback and manage your business reputation/)).toBeInTheDocument();
      expect(screen.getByText('Review Management')).toBeInTheDocument();
    });

    it('should render tab buttons with accurate counts', () => {
      render(<BusinessReviewsPageClient {...defaultProps} />);

      // Check Reviews Received tab
      const reviewsReceivedTab = screen.getByRole('button', { name: /Reviews Received/ });
      expect(reviewsReceivedTab).toBeInTheDocument();
      expect(reviewsReceivedTab).toHaveTextContent('15');

      // Check My Reviews tab
      const myReviewsTab = screen.getByRole('button', { name: /My Reviews/ });
      expect(myReviewsTab).toBeInTheDocument();
      expect(myReviewsTab).toHaveTextContent('8');
    });

    it('should display the "Reviews Received" tab content by default', () => {
      render(<BusinessReviewsPageClient {...defaultProps} />);

      // BusinessReviewListClient should be rendered
      expect(screen.getByTestId('business-review-list-client')).toBeInTheDocument();
      expect(screen.getByTestId('business-profile-id')).toHaveTextContent('business-123');

      // BusinessMyReviewListClient should not be rendered
      expect(screen.queryByTestId('business-my-review-list-client')).not.toBeInTheDocument();
    });

    it('should render BusinessReviewListClient with correct businessProfileId prop', () => {
      render(<BusinessReviewsPageClient {...defaultProps} />);

      const businessProfileIdElement = screen.getByTestId('business-profile-id');
      expect(businessProfileIdElement).toHaveTextContent('business-123');
    });

    it('should render icons correctly', () => {
      render(<BusinessReviewsPageClient {...defaultProps} />);

      expect(screen.getAllByTestId('star-icon')).toHaveLength(2); // One in header, one in My Reviews tab
      expect(screen.getByTestId('message-square-icon')).toBeInTheDocument();
    });
  });

  describe('Tab Switching (UI & State)', () => {
    it('should switch to "My Reviews" tab when clicked', () => {
      render(<BusinessReviewsPageClient {...defaultProps} />);

      // Initially, Reviews Received should be active
      expect(screen.getByTestId('business-review-list-client')).toBeInTheDocument();
      expect(screen.queryByTestId('business-my-review-list-client')).not.toBeInTheDocument();

      // Click on My Reviews tab
      const myReviewsTab = screen.getByRole('button', { name: /My Reviews/ });
      fireEvent.click(myReviewsTab);

      // Now My Reviews should be active
      expect(screen.queryByTestId('business-review-list-client')).not.toBeInTheDocument();
      expect(screen.getByTestId('business-my-review-list-client')).toBeInTheDocument();
    });

    it('should switch back to "Reviews Received" tab when clicked', () => {
      render(<BusinessReviewsPageClient {...defaultProps} />);

      // Click on My Reviews tab first
      const myReviewsTab = screen.getByRole('button', { name: /My Reviews/ });
      fireEvent.click(myReviewsTab);

      // Verify My Reviews is active
      expect(screen.getByTestId('business-my-review-list-client')).toBeInTheDocument();
      expect(screen.queryByTestId('business-review-list-client')).not.toBeInTheDocument();

      // Click on Reviews Received tab
      const reviewsReceivedTab = screen.getByRole('button', { name: /Reviews Received/ });
      fireEvent.click(reviewsReceivedTab);

      // Now Reviews Received should be active again
      expect(screen.getByTestId('business-review-list-client')).toBeInTheDocument();
      expect(screen.queryByTestId('business-my-review-list-client')).not.toBeInTheDocument();
    });

    it('should apply correct CSS classes for active and inactive tabs', () => {
      render(<BusinessReviewsPageClient {...defaultProps} />);

      const reviewsReceivedTab = screen.getByRole('button', { name: /Reviews Received/ });
      const myReviewsTab = screen.getByRole('button', { name: /My Reviews/ });

      // Initially, Reviews Received should have active styles
      expect(reviewsReceivedTab).toHaveClass('bg-white');
      expect(myReviewsTab).not.toHaveClass('bg-white');

      // Click on My Reviews tab
      fireEvent.click(myReviewsTab);

      // Now My Reviews should have active styles
      expect(myReviewsTab).toHaveClass('bg-white');
      expect(reviewsReceivedTab).not.toHaveClass('bg-white');
    });
  });

  describe('Prop Passing to Child Components', () => {
    it('should pass correct businessProfileId to BusinessReviewListClient when Reviews Received tab is active', () => {
      render(<BusinessReviewsPageClient {...defaultProps} />);

      // Reviews Received tab is active by default
      const businessProfileIdElement = screen.getByTestId('business-profile-id');
      expect(businessProfileIdElement).toHaveTextContent('business-123');
    });

    it('should render BusinessMyReviewListClient without specific props when My Reviews tab is active', () => {
      render(<BusinessReviewsPageClient {...defaultProps} />);

      // Click on My Reviews tab
      const myReviewsTab = screen.getByRole('button', { name: /My Reviews/ });
      fireEvent.click(myReviewsTab);

      // BusinessMyReviewListClient should be rendered
      const myReviewsComponent = screen.getByTestId('business-my-review-list-client');
      expect(myReviewsComponent).toBeInTheDocument();
      expect(myReviewsComponent).toHaveTextContent('My Reviews List');
    });

    it('should handle different businessProfileId values correctly', () => {
      const customProps = {
        ...defaultProps,
        businessProfileId: 'different-business-456',
      };

      render(<BusinessReviewsPageClient {...customProps} />);

      const businessProfileIdElement = screen.getByTestId('business-profile-id');
      expect(businessProfileIdElement).toHaveTextContent('different-business-456');
    });
  });

  describe('Count Display', () => {
    it('should display zero counts correctly', () => {
      const propsWithZeroCounts = {
        ...defaultProps,
        reviewsReceivedCount: 0,
        myReviewsCount: 0,
      };

      render(<BusinessReviewsPageClient {...propsWithZeroCounts} />);

      const reviewsReceivedTab = screen.getByRole('button', { name: /Reviews Received/ });
      const myReviewsTab = screen.getByRole('button', { name: /My Reviews/ });

      expect(reviewsReceivedTab).toHaveTextContent('0');
      expect(myReviewsTab).toHaveTextContent('0');
    });

    it('should display large counts correctly', () => {
      const propsWithLargeCounts = {
        ...defaultProps,
        reviewsReceivedCount: 1234,
        myReviewsCount: 567,
      };

      render(<BusinessReviewsPageClient {...propsWithLargeCounts} />);

      const reviewsReceivedTab = screen.getByRole('button', { name: /Reviews Received/ });
      const myReviewsTab = screen.getByRole('button', { name: /My Reviews/ });

      expect(reviewsReceivedTab).toHaveTextContent('1234');
      expect(myReviewsTab).toHaveTextContent('567');
    });
  });

  describe('Component Structure', () => {
    it('should render the main container with correct structure', () => {
      render(<BusinessReviewsPageClient {...defaultProps} />);

      // Check for main container
      const container = screen.getByText('Business Reviews').closest('div');
      expect(container).toBeInTheDocument();

      // Check for tabs section
      expect(screen.getByRole('button', { name: /Reviews Received/ })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /My Reviews/ })).toBeInTheDocument();

      // Check for content section
      expect(screen.getByTestId('business-review-list-client')).toBeInTheDocument();
    });

    it('should maintain component structure during tab switches', () => {
      render(<BusinessReviewsPageClient {...defaultProps} />);

      // Switch to My Reviews
      fireEvent.click(screen.getByRole('button', { name: /My Reviews/ }));

      // Header should still be present
      expect(screen.getByText('Business Reviews')).toBeInTheDocument();

      // Tabs should still be present
      expect(screen.getByRole('button', { name: /Reviews Received/ })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /My Reviews/ })).toBeInTheDocument();

      // Content should have switched
      expect(screen.getByTestId('business-my-review-list-client')).toBeInTheDocument();
    });
  });
});
