import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { redirect } from 'next/navigation';
import BusinessReviewsPage from '@/app/(dashboard)/dashboard/business/reviews/page';

// Mock the redirect function
jest.mock('next/navigation', () => ({
  redirect: jest.fn(),
}));

// Mock the BusinessReviewsPageClient component
jest.mock('@/app/(dashboard)/dashboard/business/reviews/components/BusinessReviewsPageClient', () => {
  return function MockBusinessReviewsPageClient(props: any) {
    return (
      <div data-testid="business-reviews-page-client">
        <div data-testid="business-profile-id">{props.businessProfileId}</div>
        <div data-testid="reviews-received-count">{props.reviewsReceivedCount}</div>
        <div data-testid="my-reviews-count">{props.myReviewsCount}</div>
      </div>
    );
  };
});

// Mock the Supabase client
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn(),
}));

describe('BusinessReviewsPage', () => {
  const mockCreateClient = require('@/utils/supabase/server').createClient as jest.Mock;
  const mockRedirect = redirect as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    // Set environment variables for tests
    process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key';
  });

  describe('Authentication & Authorization', () => {
    it('should redirect unauthenticated user to login', async () => {
      // Mock unauthenticated user
      mockCreateClient.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({
            data: { user: null },
            error: null,
          }),
        },
      });

      await BusinessReviewsPage();

      expect(mockRedirect).toHaveBeenCalledWith('/login?message=Please log in to view your business reviews.');
    });

    it('should redirect when auth error occurs', async () => {
      // Mock auth error
      mockCreateClient.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({
            data: { user: null },
            error: { message: 'Auth error' },
          }),
        },
      });

      await BusinessReviewsPage();

      expect(mockRedirect).toHaveBeenCalledWith('/login?message=Please log in to view your business reviews.');
    });

    it('should redirect authenticated user without business profile', async () => {
      // Mock authenticated user
      const mockFrom = jest.fn().mockReturnThis();
      const mockSelect = jest.fn().mockReturnThis();
      const mockEq = jest.fn().mockReturnThis();
      const mockSingle = jest.fn().mockResolvedValue({
        data: null,
        error: { message: 'No business profile found' },
      });

      mockCreateClient.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({
            data: { user: { id: 'user123' } },
            error: null,
          }),
        },
        from: mockFrom,
      });

      mockFrom.mockReturnValue({
        select: mockSelect,
      });
      mockSelect.mockReturnValue({
        eq: mockEq,
      });
      mockEq.mockReturnValue({
        single: mockSingle,
      });

      await BusinessReviewsPage();

      expect(mockFrom).toHaveBeenCalledWith('business_profiles');
      expect(mockSelect).toHaveBeenCalledWith('id');
      expect(mockEq).toHaveBeenCalledWith('id', 'user123');
      expect(mockRedirect).toHaveBeenCalledWith('/dashboard/business?message=Please complete your business profile first.');
    });

    it('should redirect when business profile is null', async () => {
      // Mock authenticated user
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user123' } },
        error: null,
      });

      // Mock business profile query returning null
      mockSupabaseClient.single.mockResolvedValue({
        data: null,
        error: null,
      });

      await BusinessReviewsPage();

      expect(redirect).toHaveBeenCalledWith('/dashboard/business?message=Please complete your business profile first.');
    });
  });

  describe('Initial Data Fetching & Rendering', () => {
    beforeEach(() => {
      // Mock authenticated user with business profile
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user123' } },
        error: null,
      });

      mockSupabaseClient.single.mockResolvedValue({
        data: { id: 'business123' },
        error: null,
      });
    });

    it('should fetch review counts and render BusinessReviewsPageClient with correct props', async () => {
      // Mock review counts
      const mockReviewsReceivedQuery = jest.fn().mockResolvedValue({
        count: 5,
        error: null,
      });
      const mockMyReviewsQuery = jest.fn().mockResolvedValue({
        count: 3,
        error: null,
      });

      // Set up the chain for reviews received count
      mockSupabaseClient.from.mockImplementation((table) => {
        if (table === 'business_profiles') {
          return {
            select: () => ({
              eq: () => ({
                single: mockSupabaseClient.single,
              }),
            }),
          };
        } else if (table === 'ratings_reviews') {
          const mockChain = {
            select: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            neq: jest.fn(),
          };
          
          // First call for reviews received
          mockChain.neq.mockResolvedValueOnce(mockReviewsReceivedQuery());
          // Second call for my reviews  
          mockChain.eq.mockResolvedValueOnce(mockMyReviewsQuery());
          
          return mockChain;
        }
        return mockSupabaseClient;
      });

      const result = await BusinessReviewsPage();

      expect(result).toEqual(
        expect.objectContaining({
          type: expect.any(Function),
          props: {
            businessProfileId: 'business123',
            reviewsReceivedCount: 5,
            myReviewsCount: 3,
          },
        })
      );
    });

    it('should handle zero review counts', async () => {
      // Mock zero review counts
      const mockReviewsReceivedQuery = jest.fn().mockResolvedValue({
        count: 0,
        error: null,
      });
      const mockMyReviewsQuery = jest.fn().mockResolvedValue({
        count: 0,
        error: null,
      });

      mockSupabaseClient.from.mockImplementation((table) => {
        if (table === 'business_profiles') {
          return {
            select: () => ({
              eq: () => ({
                single: mockSupabaseClient.single,
              }),
            }),
          };
        } else if (table === 'ratings_reviews') {
          const mockChain = {
            select: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            neq: jest.fn(),
          };
          
          mockChain.neq.mockResolvedValueOnce(mockReviewsReceivedQuery());
          mockChain.eq.mockResolvedValueOnce(mockMyReviewsQuery());
          
          return mockChain;
        }
        return mockSupabaseClient;
      });

      const result = await BusinessReviewsPage();

      expect(result).toEqual(
        expect.objectContaining({
          props: {
            businessProfileId: 'business123',
            reviewsReceivedCount: 0,
            myReviewsCount: 0,
          },
        })
      );
    });

    it('should handle null review counts by defaulting to 0', async () => {
      // Mock null review counts
      const mockReviewsReceivedQuery = jest.fn().mockResolvedValue({
        count: null,
        error: null,
      });
      const mockMyReviewsQuery = jest.fn().mockResolvedValue({
        count: null,
        error: null,
      });

      mockSupabaseClient.from.mockImplementation((table) => {
        if (table === 'business_profiles') {
          return {
            select: () => ({
              eq: () => ({
                single: mockSupabaseClient.single,
              }),
            }),
          };
        } else if (table === 'ratings_reviews') {
          const mockChain = {
            select: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            neq: jest.fn(),
          };
          
          mockChain.neq.mockResolvedValueOnce(mockReviewsReceivedQuery());
          mockChain.eq.mockResolvedValueOnce(mockMyReviewsQuery());
          
          return mockChain;
        }
        return mockSupabaseClient;
      });

      const result = await BusinessReviewsPage();

      expect(result).toEqual(
        expect.objectContaining({
          props: {
            businessProfileId: 'business123',
            reviewsReceivedCount: 0,
            myReviewsCount: 0,
          },
        })
      );
    });
  });

  describe('Error Handling', () => {
    beforeEach(() => {
      // Mock authenticated user with business profile
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user123' } },
        error: null,
      });

      mockSupabaseClient.single.mockResolvedValue({
        data: { id: 'business123' },
        error: null,
      });
    });

    it('should render with zero counts when error occurs during review count fetching', async () => {
      // Mock error during review count fetching
      mockSupabaseClient.from.mockImplementation((table) => {
        if (table === 'business_profiles') {
          return {
            select: () => ({
              eq: () => ({
                single: mockSupabaseClient.single,
              }),
            }),
          };
        } else if (table === 'ratings_reviews') {
          // Simulate error by throwing
          throw new Error('Database error');
        }
        return mockSupabaseClient;
      });

      const result = await BusinessReviewsPage();

      expect(result).toEqual(
        expect.objectContaining({
          props: {
            businessProfileId: 'business123',
            reviewsReceivedCount: 0,
            myReviewsCount: 0,
          },
        })
      );
    });

    it('should not crash when error occurs and still render BusinessReviewsPageClient', async () => {
      // Mock error during review count fetching
      mockSupabaseClient.from.mockImplementation((table) => {
        if (table === 'business_profiles') {
          return {
            select: () => ({
              eq: () => ({
                single: mockSupabaseClient.single,
              }),
            }),
          };
        } else if (table === 'ratings_reviews') {
          throw new Error('Unexpected database error');
        }
        return mockSupabaseClient;
      });

      // Should not throw an error
      const result = await BusinessReviewsPage();

      expect(result).toBeDefined();
      expect(result).toEqual(
        expect.objectContaining({
          type: expect.any(Function),
          props: {
            businessProfileId: 'business123',
            reviewsReceivedCount: 0,
            myReviewsCount: 0,
          },
        })
      );
    });
  });
});
