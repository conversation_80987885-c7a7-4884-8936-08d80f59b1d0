import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import '@testing-library/jest-dom';
import BusinessReviewListClient from '@/app/(dashboard)/dashboard/business/reviews/components/BusinessReviewListClient';

// Mock the actions
const mockFetchBusinessReviewsReceived = jest.fn() as jest.MockedFunction<any>;
jest.mock('@/app/(dashboard)/dashboard/business/reviews/actions', () => ({
  fetchBusinessReviewsReceived: mockFetchBusinessReviewsReceived,
}));

// Mock the child components
jest.mock('@/app/components/shared/reviews/ReviewSortDropdown', () => {
  return {
    __esModule: true,
    default: function MockReviewSortDropdown({ sortBy, onSortChange }: any) {
      return (
        <select
          data-testid="review-sort-dropdown"
          value={sortBy}
          onChange={(e) => onSortChange(e.target.value)}
        >
          <option value="newest">Newest</option>
          <option value="oldest">Oldest</option>
          <option value="highest">Highest Rating</option>
          <option value="lowest">Lowest Rating</option>
        </select>
      );
    },
  };
});

jest.mock('@/app/components/shared/reviews/ReviewCardSkeleton', () => {
  return {
    __esModule: true,
    default: function MockReviewCardSkeleton({ index }: { index: number }) {
      return <div data-testid={`review-card-skeleton-${index}`}>Loading...</div>;
    },
  };
});

jest.mock('@/app/components/shared/reviews/ReviewCard', () => {
  return {
    __esModule: true,
    default: function MockReviewCard({ review, onDeleteSuccess, isReviewsReceivedTab }: any) {
      return (
        <div data-testid={`review-card-${review.id}`}>
          <div data-testid="review-rating">{review.rating}</div>
          <div data-testid="review-text">{review.review_text}</div>
          <div data-testid="reviewer-name">{review.business_profiles?.business_name}</div>
          <div data-testid="on-delete-success">{onDeleteSuccess ? 'has-delete' : 'no-delete'}</div>
          <div data-testid="is-reviews-received-tab">{isReviewsReceivedTab ? 'true' : 'false'}</div>
        </div>
      );
    },
  };
});

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  AlertCircle: () => <div data-testid="alert-circle-icon">AlertCircle</div>,
  MessageSquare: () => <div data-testid="message-square-icon">MessageSquare</div>,
}));

// Mock window.scrollTo
const mockScrollTo = jest.fn();
Object.defineProperty(window, 'scrollTo', {
  value: mockScrollTo,
  writable: true,
});

describe('BusinessReviewListClient', () => {
  const defaultProps = {
    businessProfileId: 'business-123',
  };

  const mockReviewData = [
    {
      id: 'review-1',
      rating: 5,
      review_text: 'Great service!',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      business_profile_id: 'business-123',
      user_id: 'user-1',
      business_profiles: {
        id: 'business-123',
        business_name: 'Test Business',
        business_slug: 'test-business',
        logo_url: 'https://example.com/logo.jpg',
      },
      reviewer_type: 'customer' as const,
      reviewer_name: 'John Doe',
      reviewer_avatar: 'https://example.com/avatar.jpg',
      reviewer_slug: null,
    },
    {
      id: 'review-2',
      rating: 4,
      review_text: 'Good experience',
      created_at: '2024-01-02T00:00:00Z',
      updated_at: null,
      business_profile_id: 'business-123',
      user_id: 'user-2',
      business_profiles: {
        id: 'business-123',
        business_name: 'Another Business',
        business_slug: 'another-business',
        logo_url: null,
      },
      reviewer_type: 'business' as const,
      reviewer_name: 'Jane Smith',
      reviewer_avatar: null,
      reviewer_slug: 'jane-smith-business',
    },
  ];

  const mockSuccessResponse = {
    success: true,
    data: {
      items: mockReviewData,
      currentPage: 1,
      totalCount: 2,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockScrollTo.mockClear();
  });

  describe('Initial Data Fetching & Rendering', () => {
    it('should call fetchBusinessReviewsReceived with correct default parameters on mount', async () => {
      mockFetchBusinessReviewsReceived.mockResolvedValue(mockSuccessResponse);

      render(<BusinessReviewListClient {...defaultProps} />);

      await waitFor(() => {
        expect(mockFetchBusinessReviewsReceived).toHaveBeenCalledWith(
          'business-123',
          1, // default page
          10, // default limit
          'newest' // default sort
        );
      });
    });

    it('should render ReviewCard components with correct props when data is fetched', async () => {
      mockFetchBusinessReviewsReceived.mockResolvedValue(mockSuccessResponse);

      render(<BusinessReviewListClient {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByTestId('review-card-review-1')).toBeInTheDocument();
        expect(screen.getByTestId('review-card-review-2')).toBeInTheDocument();
      });

      // Check ReviewCard props
      expect(screen.getByTestId('review-card-review-1')).toBeInTheDocument();
      expect(screen.getByTestId('review-card-review-2')).toBeInTheDocument();

      // Check that onDeleteSuccess is null and isReviewsReceivedTab is true
      const deleteSuccessElements = screen.getAllByTestId('on-delete-success');
      const isReviewsReceivedElements = screen.getAllByTestId('is-reviews-received-tab');

      deleteSuccessElements.forEach(element => {
        expect(element).toHaveTextContent('no-delete');
      });

      isReviewsReceivedElements.forEach(element => {
        expect(element).toHaveTextContent('true');
      });
    });

    it('should display empty state message when no reviews are returned', async () => {
      mockFetchBusinessReviewsReceived.mockResolvedValue({
        success: true,
        data: {
          items: [],
          currentPage: 1,
          totalCount: 0,
        },
      });

      render(<BusinessReviewListClient {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('No reviews received yet')).toBeInTheDocument();
      });

      expect(screen.getByText(/When customers review your business/)).toBeInTheDocument();
      expect(screen.getByTestId('message-square-icon')).toBeInTheDocument();
    });
  });

  describe('Loading States', () => {
    it('should display ReviewCardSkeleton components while loading', () => {
      mockFetchBusinessReviewsReceived.mockImplementation(() => new Promise(() => {})); // Never resolves

      render(<BusinessReviewListClient {...defaultProps} />);

      // Should show 6 skeleton components
      for (let i = 0; i < 6; i++) {
        expect(screen.getByTestId(`review-card-skeleton-${i}`)).toBeInTheDocument();
      }
    });

    it('should hide ReviewCardSkeleton components after data is loaded', async () => {
      mockFetchBusinessReviewsReceived.mockResolvedValue(mockSuccessResponse);

      render(<BusinessReviewListClient {...defaultProps} />);

      await waitFor(() => {
        expect(screen.queryByTestId('review-card-skeleton-0')).not.toBeInTheDocument();
      });

      // All skeletons should be gone
      for (let i = 0; i < 6; i++) {
        expect(screen.queryByTestId(`review-card-skeleton-${i}`)).not.toBeInTheDocument();
      }
    });
  });

  describe('Error Handling', () => {
    it('should display Alert component with destructive variant when fetchBusinessReviewsReceived throws error', async () => {
      mockFetchBusinessReviewsReceived.mockRejectedValue(new Error('Network error'));

      render(<BusinessReviewListClient {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByRole('alert')).toBeInTheDocument();
      });

      expect(screen.getByText('Error')).toBeInTheDocument();
      expect(screen.getByText('Failed to load reviews. Please try again.')).toBeInTheDocument();
      expect(screen.getByTestId('alert-circle-icon')).toBeInTheDocument();
    });

    it('should display error message when fetchBusinessReviewsReceived returns error', async () => {
      mockFetchBusinessReviewsReceived.mockResolvedValue({
        success: false,
        error: 'Custom error message',
      });

      render(<BusinessReviewListClient {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByRole('alert')).toBeInTheDocument();
      });

      expect(screen.getByText('Custom error message')).toBeInTheDocument();
    });

    it('should not crash when error occurs', async () => {
      mockFetchBusinessReviewsReceived.mockRejectedValue(new Error('Network error'));

      expect(() => {
        render(<BusinessReviewListClient {...defaultProps} />);
      }).not.toThrow();

      await waitFor(() => {
        expect(screen.getByRole('alert')).toBeInTheDocument();
      });
    });
  });

  describe('Sorting Functionality', () => {
    it('should update sortBy state and call fetchBusinessReviewsReceived with new sort option', async () => {
      mockFetchBusinessReviewsReceived.mockResolvedValue(mockSuccessResponse);

      render(<BusinessReviewListClient {...defaultProps} />);

      await waitFor(() => {
        expect(mockFetchBusinessReviewsReceived).toHaveBeenCalledTimes(1);
      });

      // Change sort option
      const sortDropdown = screen.getByTestId('review-sort-dropdown');
      fireEvent.change(sortDropdown, { target: { value: 'oldest' } });

      await waitFor(() => {
        expect(mockFetchBusinessReviewsReceived).toHaveBeenCalledWith(
          'business-123',
          1, // page should reset to 1
          10,
          'oldest' // new sort option
        );
      });

      expect(mockFetchBusinessReviewsReceived).toHaveBeenCalledTimes(2);
    });

    it('should reset currentPage to 1 when sort changes', async () => {
      // Start with multiple pages
      const multiPageResponse = {
        success: true,
        data: {
          items: mockReviewData,
          currentPage: 3,
          totalCount: 50,
        },
      };

      mockFetchBusinessReviewsReceived.mockResolvedValue(multiPageResponse);

      render(<BusinessReviewListClient {...defaultProps} />);

      await waitFor(() => {
        expect(mockFetchBusinessReviewsReceived).toHaveBeenCalledTimes(1);
      });

      // Change sort option
      const sortDropdown = screen.getByTestId('review-sort-dropdown');
      fireEvent.change(sortDropdown, { target: { value: 'highest' } });

      await waitFor(() => {
        expect(mockFetchBusinessReviewsReceived).toHaveBeenLastCalledWith(
          'business-123',
          1, // Should reset to page 1
          10,
          'highest'
        );
      });
    });
  });

  describe('Pagination Functionality', () => {
    it('should call handlePageChange and fetchBusinessReviewsReceived when pagination link is clicked', async () => {
      const multiPageResponse = {
        success: true,
        data: {
          items: mockReviewData,
          currentPage: 1,
          totalCount: 25, // More than 10 to trigger pagination
        },
      };

      mockFetchBusinessReviewsReceived.mockResolvedValue(multiPageResponse);

      render(<BusinessReviewListClient {...defaultProps} />);

      await waitFor(() => {
        expect(mockFetchBusinessReviewsReceived).toHaveBeenCalledTimes(1);
      });

      // Should show pagination since totalCount > perPage
      expect(screen.getByText('2')).toBeInTheDocument(); // Page 2 link
      expect(screen.getByText('3')).toBeInTheDocument(); // Page 3 link

      // Click on page 2
      fireEvent.click(screen.getByText('2'));

      await waitFor(() => {
        expect(mockFetchBusinessReviewsReceived).toHaveBeenCalledWith(
          'business-123',
          2, // new page
          10,
          'newest'
        );
      });

      expect(mockScrollTo).toHaveBeenCalledWith({ top: 0, behavior: 'smooth' });
    });

    it('should disable Previous button when on first page', async () => {
      const multiPageResponse = {
        success: true,
        data: {
          items: mockReviewData,
          currentPage: 1,
          totalCount: 25,
        },
      };

      mockFetchBusinessReviewsReceived.mockResolvedValue(multiPageResponse);

      render(<BusinessReviewListClient {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Previous')).toBeInTheDocument();
      });

      const previousButton = screen.getByText('Previous').closest('a');
      expect(previousButton).toHaveClass('pointer-events-none', 'opacity-50');
    });

    it('should disable Next button when on last page', async () => {
      const lastPageResponse = {
        success: true,
        data: {
          items: mockReviewData,
          currentPage: 3,
          totalCount: 25,
        },
      };

      mockFetchBusinessReviewsReceived.mockResolvedValue(lastPageResponse);

      render(<BusinessReviewListClient {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Next')).toBeInTheDocument();
      });

      const nextButton = screen.getByText('Next').closest('a');
      expect(nextButton).toHaveClass('pointer-events-none', 'opacity-50');
    });

    it('should not render pagination when totalPages <= 1', async () => {
      const singlePageResponse = {
        success: true,
        data: {
          items: mockReviewData,
          currentPage: 1,
          totalCount: 5, // Less than perPage (10)
        },
      };

      mockFetchBusinessReviewsReceived.mockResolvedValue(singlePageResponse);

      render(<BusinessReviewListClient {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByTestId('review-card-review-1')).toBeInTheDocument();
      });

      expect(screen.queryByText('Previous')).not.toBeInTheDocument();
      expect(screen.queryByText('Next')).not.toBeInTheDocument();
    });
  });

  describe('ReviewCard Props', () => {
    it('should construct business_profiles correctly for customer reviewer', async () => {
      const customerReviewData = [{
        ...mockReviewData[0],
        reviewer_type: 'customer' as const,
        reviewer_name: 'John Customer',
        reviewer_avatar: 'https://example.com/customer.jpg',
        reviewer_slug: null,
      }];

      mockFetchBusinessReviewsReceived.mockResolvedValue({
        success: true,
        data: {
          items: customerReviewData,
          currentPage: 1,
          totalCount: 1,
        },
      });

      render(<BusinessReviewListClient {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByTestId('reviewer-name')).toHaveTextContent('John Customer');
      });
    });

    it('should construct business_profiles correctly for business reviewer', async () => {
      const businessReviewData = [{
        ...mockReviewData[1],
        reviewer_type: 'business' as const,
        reviewer_name: 'Business Name',
        reviewer_avatar: 'https://example.com/business.jpg',
        reviewer_slug: 'business-slug',
      }];

      mockFetchBusinessReviewsReceived.mockResolvedValue({
        success: true,
        data: {
          items: businessReviewData,
          currentPage: 1,
          totalCount: 1,
        },
      });

      render(<BusinessReviewListClient {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByTestId('reviewer-name')).toHaveTextContent('Business Name');
      });
    });

    it('should handle anonymous reviewer correctly', async () => {
      const anonymousReviewData = [{
        ...mockReviewData[0],
        reviewer_name: null,
        reviewer_avatar: null,
        reviewer_slug: null,
      }];

      mockFetchBusinessReviewsReceived.mockResolvedValue({
        success: true,
        data: {
          items: anonymousReviewData,
          currentPage: 1,
          totalCount: 1,
        },
      });

      render(<BusinessReviewListClient {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByTestId('reviewer-name')).toHaveTextContent('Anonymous User');
      });
    });
  });
});
